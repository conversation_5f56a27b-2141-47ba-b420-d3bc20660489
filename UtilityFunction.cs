using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using System.Net;
using System.Text.Json;
using PasswordHistoryValidator.Shared;
using Azure.Storage.Blobs;

namespace PasswordHistoryValidator;

public class UtilityFunction : BaseFunctionService
{
    private readonly ILogger<UtilityFunction> _logger;
    private readonly BlobServiceClient _blobServiceClient;

    public UtilityFunction(
        ILogger<UtilityFunction> logger,
        BlobServiceClient blobServiceClient,
        JsonSerializerOptions jsonOptions) : base(jsonOptions)
    {
        _logger = logger;
        _blobServiceClient = blobServiceClient;
    }

    [Function("UtilityService")]
    public async Task<HttpResponseData> Run(
        [HttpTrigger(AuthorizationLevel.Function, "post", "options", Route = null)] HttpRequestData req,
        FunctionContext executionContext,
        CancellationToken cancellationToken)
    {
        var correlationId = Guid.NewGuid().ToString("N")[..8];
        var logger = executionContext.GetLogger<UtilityFunction>();

        try
        {
            logger.LogInformation("Utility service triggered [CorrelationId: {CorrelationId}]", correlationId);
            logger.LogInformation("Request URL: {Url} [CorrelationId: {CorrelationId}]", req.Url, correlationId);
            logger.LogInformation("Request Method: {Method} [CorrelationId: {CorrelationId}]", req.Method, correlationId);


            if (req.Method.Equals("OPTIONS", StringComparison.OrdinalIgnoreCase))
            {
                logger.LogInformation("Handling OPTIONS request [CorrelationId: {CorrelationId}]", correlationId);
                return CreateCorsResponse(req);
            }


            var query = System.Web.HttpUtility.ParseQueryString(req.Url.Query);
            var operation = query["operation"] ?? string.Empty;

            logger.LogInformation("Processing operation: {Operation} [CorrelationId: {CorrelationId}]", operation, correlationId);


            var response = operation.ToLowerInvariant() switch
            {
                "health" => await HandleHealthCheck(req, correlationId),
                "cleanup-tokens" => await HandleTokenCleanup(req, correlationId, cancellationToken),
                "stats" => await HandleSystemStats(req, correlationId, cancellationToken),
                _ => await CreateErrorResponse(req, $"Invalid operation: {operation}", correlationId)
            };

            logger.LogInformation("Operation {Operation} completed [CorrelationId: {CorrelationId}]", operation, correlationId);
            return response;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Utility service error [CorrelationId: {CorrelationId}]", correlationId);
            return await CreateErrorResponse(req, "Service error", correlationId);
        }
    }

    private async Task<HttpResponseData> HandleHealthCheck(HttpRequestData req, string correlationId)
    {
        var healthStatus = new
        {
            status = "healthy",
            timestamp = DateTime.UtcNow,
            version = "3.0.0-simplified",
            services = new
            {
                blobStorage = await CheckBlobStorageHealth(),
                configuration = "healthy"
            }
        };

        return await CreateJsonResponse(req, healthStatus, HttpStatusCode.OK, correlationId);
    }

    private async Task<HttpResponseData> HandleTokenCleanup(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        var cleanupResults = await CleanupExpiredTokens(cancellationToken);

        return await CreateJsonResponse(req, new
        {
            message = "Token cleanup completed",
            tokensRemoved = cleanupResults.TokensRemoved,
            tokensProcessed = cleanupResults.TokensProcessed,
            timestamp = DateTime.UtcNow
        }, HttpStatusCode.OK, correlationId);
    }

    private async Task<HttpResponseData> HandleSystemStats(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        var stats = await GetSystemStatistics(cancellationToken);
        return await CreateJsonResponse(req, stats, HttpStatusCode.OK, correlationId);
    }

    private async Task<string> CheckBlobStorageHealth()
    {
        try
        {
            var containerClient = _blobServiceClient.GetBlobContainerClient("passwordhistory");
            await containerClient.CreateIfNotExistsAsync();
            return "healthy";
        }
        catch
        {
            return "unhealthy";
        }
    }

    private async Task<CleanupResults> CleanupExpiredTokens(CancellationToken cancellationToken)
    {
        var results = new CleanupResults();
        var containerClient = _blobServiceClient.GetBlobContainerClient("resettokens");

        await foreach (var blobItem in containerClient.GetBlobsAsync(cancellationToken: cancellationToken))
        {
            results.TokensProcessed++;

            // individual token processing errors
            try
            {
                var blobClient = containerClient.GetBlobClient(blobItem.Name);
                var downloadResult = await blobClient.DownloadContentAsync(cancellationToken);
                var tokenDataJson = downloadResult.Value.Content.ToString();
                var tokenData = JsonSerializer.Deserialize<ResetTokenData>(tokenDataJson, JsonOptions);

                if (tokenData != null && (tokenData.ExpiresUtc < DateTime.UtcNow || tokenData.Used))
                {
                    await blobClient.DeleteIfExistsAsync(cancellationToken: cancellationToken);
                    results.TokensRemoved++;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error processing token blob {BlobName} during cleanup", blobItem.Name);
            }
        }

        return results;
    }

    private async Task<object> GetSystemStatistics(CancellationToken cancellationToken)
    {
        var passwordHistoryContainer = _blobServiceClient.GetBlobContainerClient("passwordhistory");
        var resetTokensContainer = _blobServiceClient.GetBlobContainerClient("resettokens");

        var passwordHistoryCount = 0;
        var resetTokensCount = 0;
        var activeTokensCount = 0;

        await foreach (var blobItem in passwordHistoryContainer.GetBlobsAsync(cancellationToken: cancellationToken))
        {
            passwordHistoryCount++;
        }


        await foreach (var blobItem in resetTokensContainer.GetBlobsAsync(cancellationToken: cancellationToken))
        {
            resetTokensCount++;

            // individual token processing errors
            try
            {
                var blobClient = resetTokensContainer.GetBlobClient(blobItem.Name);
                var downloadResult = await blobClient.DownloadContentAsync(cancellationToken);
                var tokenDataJson = downloadResult.Value.Content.ToString();
                var tokenData = JsonSerializer.Deserialize<ResetTokenData>(tokenDataJson, JsonOptions);

                if (tokenData != null && !tokenData.Used && tokenData.ExpiresUtc > DateTime.UtcNow)
                {
                    activeTokensCount++;
                }
            }
            catch
            {
                // Skip invalid tokens during statistics gathering
            }
        }

        return new
        {
            passwordHistoryEntries = passwordHistoryCount,
            totalResetTokens = resetTokensCount,
            activeResetTokens = activeTokensCount,
            expiredTokens = resetTokensCount - activeTokensCount,
            timestamp = DateTime.UtcNow
        };
    }





    private class CleanupResults
    {
        public int TokensProcessed { get; set; }
        public int TokensRemoved { get; set; }
    }
}
